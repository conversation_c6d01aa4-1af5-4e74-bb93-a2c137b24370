import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

// Loading spinner component
const LoadingSpinner = ({ size = 'sm' }: { size?: 'sm' | 'md' | 'lg' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };
  
  return (
    <svg
      className={cn('animate-spin', sizeClasses[size])}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

// Button variants using class-variance-authority for type safety
const buttonVariants = cva(
  [
    // Base styles - foundational design
    'relative inline-flex items-center justify-center',
    'font-semibold tracking-tight',
    'border border-transparent',
    'transition-all duration-200 ease-out',
    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white',
    'disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed',
    'select-none touch-manipulation',
    'overflow-hidden',
    
    // Typography and spacing
    'whitespace-nowrap text-center',
    
    // Enhanced interaction states
    'active:scale-[0.98] active:transition-transform active:duration-75',
    
    // Accessibility improvements
    'focus-visible:ring-2 focus-visible:ring-offset-2',
  ],
  {
    variants: {
      variant: {
        primary: [
          'bg-gradient-to-r from-blue-600 to-blue-700',
          'hover:from-blue-700 hover:to-blue-800',
          'text-white border-blue-600',
          'shadow-lg shadow-blue-600/25',
          'hover:shadow-xl hover:shadow-blue-600/30',
          'focus:ring-blue-500',
          'active:from-blue-800 active:to-blue-900',
        ],
        secondary: [
          'bg-gradient-to-r from-slate-100 to-slate-200',
          'hover:from-slate-200 hover:to-slate-300',
          'text-slate-900 border-slate-300',
          'shadow-sm',
          'hover:shadow-md',
          'focus:ring-slate-400',
          'dark:from-slate-800 dark:to-slate-700',
          'dark:hover:from-slate-700 dark:hover:to-slate-600',
          'dark:text-slate-100 dark:border-slate-600',
        ],
        success: [
          'bg-gradient-to-r from-emerald-600 to-emerald-700',
          'hover:from-emerald-700 hover:to-emerald-800',
          'text-white border-emerald-600',
          'shadow-lg shadow-emerald-600/25',
          'hover:shadow-xl hover:shadow-emerald-600/30',
          'focus:ring-emerald-500',
        ],
        danger: [
          'bg-gradient-to-r from-red-600 to-red-700',
          'hover:from-red-700 hover:to-red-800',
          'text-white border-red-600',
          'shadow-lg shadow-red-600/25',
          'hover:shadow-xl hover:shadow-red-600/30',
          'focus:ring-red-500',
        ],
        warning: [
          'bg-gradient-to-r from-amber-500 to-amber-600',
          'hover:from-amber-600 hover:to-amber-700',
          'text-white border-amber-500',
          'shadow-lg shadow-amber-500/25',
          'hover:shadow-xl hover:shadow-amber-500/30',
          'focus:ring-amber-400',
        ],
        outline: [
          'bg-transparent hover:bg-slate-50',
          'text-slate-700 border-slate-300',
          'hover:border-slate-400',
          'shadow-sm hover:shadow-md',
          'focus:ring-slate-400',
          'dark:hover:bg-slate-800',
          'dark:text-slate-300 dark:border-slate-600',
          'dark:hover:border-slate-500',
        ],
        ghost: [
          'bg-transparent hover:bg-slate-100',
          'text-slate-700 border-transparent',
          'hover:border-slate-200',
          'focus:ring-slate-400',
          'dark:hover:bg-slate-800',
          'dark:text-slate-300',
          'dark:hover:border-slate-700',
        ],
        link: [
          'bg-transparent hover:bg-transparent',
          'text-blue-600 hover:text-blue-700',
          'border-transparent',
          'underline-offset-4 hover:underline',
          'focus:ring-blue-500',
          'shadow-none',
          'dark:text-blue-400 dark:hover:text-blue-300',
        ],
      },
      size: {
        xs: ['text-xs', 'px-2.5 py-1.5', 'rounded-md', 'min-h-[28px]'],
        sm: ['text-sm', 'px-3 py-2', 'rounded-md', 'min-h-[32px]'],
        md: ['text-sm', 'px-4 py-2.5', 'rounded-lg', 'min-h-[40px]'],
        lg: ['text-base', 'px-6 py-3', 'rounded-lg', 'min-h-[44px]'],
        xl: ['text-lg', 'px-8 py-4', 'rounded-xl', 'min-h-[52px]'],
      },
      fullWidth: {
        true: 'w-full',
        false: 'w-auto',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      fullWidth: false,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  /** Icon to display on the left side */
  leftIcon?: React.ReactNode;
  /** Icon to display on the right side */
  rightIcon?: React.ReactNode;
  /** Loading state - shows spinner and disables interaction */
  isLoading?: boolean;
  /** Loading text to display when isLoading is true */
  loadingText?: string;
  /** Make button full width */
  fullWidth?: boolean;
  /** Accessible label for screen readers */
  'aria-label'?: string;
  /** Additional description for complex buttons */
  'aria-describedby'?: string;
}

/**
 * Enterprise-grade Button component with comprehensive variant support,
 * accessibility features, and sophisticated styling
 */
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      leftIcon,
      rightIcon,
      isLoading = false,
      loadingText,
      children,
      className,
      disabled,
      type = 'button',
      'aria-label': ariaLabel,
      'aria-describedby': ariaDescribedby,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || isLoading;
    
    // Determine spinner size based on button size
    const spinnerSize = size === 'xs' || size === 'sm' ? 'sm' : size === 'xl' ? 'lg' : 'md';
    
    // Content to display - either loading state or normal content
    const buttonContent = isLoading ? (
      <>
        <LoadingSpinner size={spinnerSize} />
        {loadingText && <span className="ml-2">{loadingText}</span>}
      </>
    ) : (
      <>
        {leftIcon && (
          <span className="inline-flex items-center justify-center mr-2" aria-hidden="true">
            {leftIcon}
          </span>
        )}
        <span>{children}</span>
        {rightIcon && (
          <span className="inline-flex items-center justify-center ml-2" aria-hidden="true">
            {rightIcon}
          </span>
        )}
      </>
    );

    return (
      <button
        ref={ref}
        type={type}
        className={cn(
          buttonVariants({ variant, size, fullWidth }),
          className
        )}
        disabled={isDisabled}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedby}
        aria-disabled={isDisabled}
        {...props}
      >
        {buttonContent}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
export default Button;